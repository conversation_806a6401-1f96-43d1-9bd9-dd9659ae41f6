import os
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict


class QueueConfig(BaseSettings):
    """Configuration for Upstash QStash message queue."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    # Cloudinary Configuration
    cloudinary_cloud_name: str = ""
    cloudinary_api_key: str = ""
    cloudinary_api_secret: str = ""
    cloudinary_upload_preset: str = ""  # For unsigned uploads (optional)

    # Backend API Configuration
    backend_api_base_url: str = (
        ""  # URL to the frontend API (e.g., http://localhost:3000)
    )


# Global configuration instance
_queue_config: Optional[QueueConfig] = None


def get_queue_config() -> QueueConfig:
    """Get the global queue configuration instance."""
    global _queue_config
    if _queue_config is None:
        _queue_config = QueueConfig()
    return _queue_config
