import os
import sys
import logging
from typing import Optional
from dotenv import load_dotenv
from config.queue_config import get_queue_config

# Load environment variables like x.py does
dotenv_path = os.path.join(os.path.dirname(__file__), "..", ".env")
load_dotenv(dotenv_path=dotenv_path)

logger = logging.getLogger(__name__)

try:
    import google.generativeai as genai
except ImportError:
    logger.error("Google Generative AI library not installed")
    genai = None


class NarrationService:
    """Service for generating educational narration using Google Gemini AI."""

    def __init__(self):
        self.config = get_queue_config()
        self.model = None
        self._initialize_gemini()

    def _initialize_gemini(self):
        """Initialize Google Gemini AI model."""
        try:
            if not genai:
                raise ImportError("Google Generative AI library not available")

            google_api_key = os.getenv("GOOGLE_API_KEY")
            if not google_api_key:
                raise ValueError("GOOGLE_API_KEY environment variable not set")

            genai.configure(api_key=google_api_key)
            self.model = genai.GenerativeModel("gemini-1.5-flash")
            logger.info("Gemini AI model initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Gemini AI: {e}")
            self.model = None

    def generate_narration(
        self, description: str = "", topic_name: str = "", duration: int = 30
    ) -> str:

        if not self.model:
            logger.error("Gemini model not available for narration generation")
            return ""

        system_prompt = """
    You are a precise and disciplined academic narrator. Your role is to create concise, tempo-perfect voiceover scripts for educational animations. You do not speak casually or emotionally — your delivery is calm, confident, and highly informative.

    🔒 STRICT RULES:
    - Do NOT use greetings, conclusions, or filler phrases (e.g., "Good morning", "Let's begin", "In conclusion", etc.)
    - DO NOT use self-referencing lines like "In this video" or "You'll see..."
    - ONLY describe and explain the concept visually and theoretically, step by step, with precision.

    🎯 OBJECTIVE:
    - The narration must match an animation running at **0.75x speed**
    - This means approximately **2.2 words per second**
    - For {duration} seconds, your narration must stay within **{int(duration * 2.2)} to {int(duration * 2.4)} words**, never exceeding this limit
    - Maximum: **500 words**, even for long videos
    - Must sound natural at that pace, not rushed
    - Must feel like it's paced with the animation

    Your narration style should resemble a calm university lecture over a visual animation — no excitement, no drama, no greetings, no opinions — just facts and concept explanations.

    Do not mention Manim or the code. Focus ONLY on the visuals and the educational concept.
    """

        if not description or not topic_name:
            logger.error("Both topic and description must be provided.")
            return ""

        user_prompt = f"""
    You are generating narration for an educational Manim animation.

    📌 Topic: {topic_name}
    🖼️ Visual Description: {description}
    ⏱️ Duration: {duration} seconds

    ✳️ Instructions:
    - Narration must explain the core concept of "{topic_name}" based on what is being visually shown.
    - Narration should be accurate, aligned with the visual sequence, and conceptually rich.
    - Maintain a calm, formal, academic tone — free of any conversational or emotional elements.
    - Fit the entire narration within {duration} seconds for a 0.75 tempo, which means staying within {int(duration * 2.2)} to {int(duration * 2.4)} words.
    - The narration must not exceed the time or word count — do not overshoot.

    🛑 DO NOT:
    - Greet the viewer or refer to the audience
    - Refer to time, the script, or yourself
    - Mention the code or technical animation details

    ✅ DO:
    - Focus entirely on the conceptual explanation and visual flow
    - Match each visual moment with the appropriate explanation
    - Ensure the narration is fully paced and synchronized for voiceover

    Return only the narration script, nothing else.
    """

        try:
            messages = self._transform_to_gemini_format(
                [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ]
            )

            response = self.model.generate_content(messages)
            narration = response.text.strip()

            word_count = len(narration.split())
            logger.info(
                f"Generated narration ({word_count} words, target: {duration} sec @ 0.75 tempo)"
            )
            return narration

        except Exception as e:
            logger.error(f"Narration generation failed: {e}")
            return ""

    def _transform_to_gemini_format(self, messages_chatgpt):
        """Transform ChatGPT format messages to Gemini format."""
        messages_gemini = []
        system_prompt = ""

        for message in messages_chatgpt:
            if message["role"] == "system":
                system_prompt = message["content"]
            elif message["role"] == "user":
                messages_gemini.append({"role": "user", "parts": [message["content"]]})
            elif message["role"] == "assistant":
                messages_gemini.append({"role": "model", "parts": [message["content"]]})

        # Prepend system prompt to first user message
        if system_prompt and messages_gemini:
            messages_gemini[0]["parts"].insert(0, f"*{system_prompt}*")

        return messages_gemini

    def is_available(self) -> bool:
        """Check if the narration service is available."""
        return self.model is not None


# Global service instance
_narration_service: Optional[NarrationService] = None


def get_narration_service() -> NarrationService:
    """Get the global NarrationService instance."""
    global _narration_service
    if _narration_service is None:
        _narration_service = NarrationService()
    return _narration_service
