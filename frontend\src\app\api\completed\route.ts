import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { Resend } from "resend";
import { promises as fs } from "fs";
import path from "path";

const resend = new Resend(process.env.AUTH_RESEND_KEY);
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

export async function POST(request: NextRequest) {
  try {
    const { entryId, videoUrl, status } = await request.json();

    if (!entryId || !videoUrl || !status) {
      return NextResponse.json(
        { error: "Missing required parameters: entryId, videoUrl, status" },
        { status: 400 }
      );
    }

    console.log("🎉 Video processing completed:", {
      entryId,
      videoUrl,
      status,
      timestamp: new Date().toISOString(),
    });

    // Update database: set isGenerating to false, update videoUrl, and set queue position to -1
    try {
      const updatedEntry = await db.entries.update({
        where: { id: entryId },
        data: {
          isGenerating: false,
          videoUrl: videoUrl,
          queuePosition: -1, // Mark as completed
          updatedAt: new Date(),
        },
      });

      console.log("✅ Database updated for entry:", entryId);

      // Decrease all queue positions by 1 for entries that are not completed (-1)
      await db.entries.updateMany({
        where: {
          queuePosition: {
            gt: 0, // Greater than 0 (not completed)
          },
        },
        data: {
          queuePosition: {
            decrement: 1,
          },
        },
      });

      console.log("✅ Queue positions updated - all entries moved up by 1");

      // Check if there's a new job at position 1 that needs to be started
      const nextJob = await db.entries.findFirst({
        where: {
          queuePosition: 1,
          isGenerating: true,
        },
        select: {
          id: true,
          scripts: true,
          topicName: true,
        },
      });

      if (nextJob) {
        console.log("🚀 Starting next job in queue:", nextJob.id);
        await startBackendProcessing(
          nextJob.id,
          Array.isArray(nextJob.scripts) ? nextJob.scripts : [],
          nextJob.topicName ?? ""
        );
      } else {
        console.log("📋 No more jobs in queue");
      }

      // Get user data for email
      const user = await db.user.findFirst({
        where: { id: updatedEntry.userId },
      });

      // Send completion email to user
      if (user?.email) {
        try {
          await sendCompletionEmail(user.email, updatedEntry.prompt, videoUrl);
          console.log("📧 Completion email sent to:", user.email);
        } catch (emailError) {
          console.error("❌ Failed to send completion email:", emailError);
          // Don't fail the request if email fails
        }
      }

      // Clean up media directory after successful completion
      try {
        await cleanupMediaDirectory();
        console.log("🧹 Media directory cleaned up successfully");
      } catch (cleanupError) {
        console.error("❌ Failed to cleanup media directory:", cleanupError);
        // Don't fail the request if cleanup fails
      }

      return NextResponse.json(
        {
          success: true,
          message: "Video completion processed successfully",
          entryId,
          videoUrl,
          status,
        },
        { status: 200 }
      );
    } catch (dbError) {
      console.error("❌ Database update failed:", dbError);
      return NextResponse.json(
        { error: "Failed to update database" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("❌ Error processing completion notification:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function startBackendProcessing(entryId: string, scripts: any[], topicName: string) {
  try {
    console.log("📤 Sending batch render request to backend for entry:", entryId);

    // Prepare batch render payload
    const renderPayload = {
      topicName: topicName,
      entryId: entryId,
      scripts: scripts.map((script) => ({
        manim_code: script.manim_code,
        description: script.description,
      })),
      priority: 0,
    };

    const response = await fetch(`${BACKEND_URL}/batch_render`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(renderPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Backend error: ${response.status} - ${errorText}`);
      throw new Error(`Backend error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log("✅ Backend processing started successfully:", result);
  } catch (error) {
    console.error("❌ Failed to start backend processing:", error);
    // Note: We don't throw here to avoid breaking the completion flow
    // The job will remain in queue and can be retried
  }
}

async function sendCompletionEmail(email: string, prompt: string, videoUrl: string) {
  const emailHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Video is Ready!</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
      <div style="max-width: 600px; margin: 0 auto; padding: 40px 20px;">
        <div style="background: white; border-radius: 16px; padding: 40px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);">
          <!-- Header -->
          <div style="text-align: center; margin-bottom: 32px;">
            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #13a564 0%, #2089f9 100%); border-radius: 50%; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
              <span style="color: white; font-size: 32px;">🎬</span>
            </div>
            <h1 style="color: #1a1a1a; font-size: 28px; font-weight: 700; margin: 0; line-height: 1.2;">Your Video is Ready!</h1>
          </div>

          <!-- Content -->
          <div style="margin-bottom: 32px;">
            <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 0 0 16px;">
              Great news! Your animated explanation video has been successfully generated and is ready to view.
            </p>

            <div style="background: #f7fafc; border-radius: 12px; padding: 20px; margin: 20px 0;">
              <h3 style="color: #2d3748; font-size: 16px; font-weight: 600; margin: 0 0 8px;">Topic:</h3>
              <p style="color: #4a5568; font-size: 14px; margin: 0; font-style: italic;">"${prompt}"</p>
            </div>

            <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin: 16px 0;">
              Your video includes detailed animations, professional narration, and interactive quizzes to help you master the topic.
            </p>
          </div>

          <!-- CTA Button -->
          <div style="text-align: center; margin: 32px 0;">
            <a href="${process.env.NEXTAUTH_URL}/library" style="display: inline-block; background: linear-gradient(135deg, #13a564 0%, #2089f9 100%); color: white; text-decoration: none; padding: 16px 32px; border-radius: 12px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 12px rgba(19, 165, 100, 0.3);">
              View Your Video
            </a>
          </div>

          <!-- Footer -->
          <div style="text-align: center; padding-top: 24px; border-top: 1px solid #e2e8f0;">
            <p style="color: #718096; font-size: 14px; margin: 0;">
              Happy learning! 🚀<br>
              The ClarifAI Team
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  await resend.emails.send({
    from: 'ClarifAI <<EMAIL>>',
    to: [email],
    subject: '🎉 Your Video is Ready!',
    html: emailHtml,
  });
}

async function cleanupMediaDirectory() {
  try {
    // Path to the backend media directory
    const mediaPath = path.join(process.cwd(), '..', 'backend', 'media');

    // Check if media directory exists
    try {
      await fs.access(mediaPath);
    } catch {
      console.log("📁 Media directory doesn't exist, nothing to clean");
      return;
    }

    // Get all items in media directory
    const items = await fs.readdir(mediaPath);

    for (const item of items) {
      const itemPath = path.join(mediaPath, item);
      const stat = await fs.stat(itemPath);

      if (stat.isDirectory()) {
        // Remove directory and all its contents
        await fs.rm(itemPath, { recursive: true, force: true });
        console.log(`🗑️ Removed directory: ${item}`);
      } else {
        // Remove file
        await fs.unlink(itemPath);
        console.log(`🗑️ Removed file: ${item}`);
      }
    }

    console.log("✅ Media directory completely cleaned");
  } catch (error) {
    console.error("❌ Error cleaning media directory:", error);
    throw error;
  }
}
