import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { entryId, scripts, topicName } = await request.json();

    if (!entryId || !scripts || !topicName) {
      return NextResponse.json(
        { error: "Missing required fields: entryId, scripts, topicName" },
        { status: 400 }
      );
    }

    console.log("🚀 Starting generation for entry:", entryId);

    // Get the highest queue position currently in the database
    const highestQueueEntry = await db.entries.findFirst({
      where: {
        queuePosition: {
          not: -1, // Exclude completed entries
        },
      },
      orderBy: {
        queuePosition: 'desc',
      },
      select: {
        queuePosition: true,
      },
    });

    // Calculate new queue position
    const newQueuePosition = highestQueueEntry?.queuePosition ? highestQueueEntry.queuePosition + 1 : 1;

    console.log(`📋 Assigning queue position: ${newQueuePosition}`);

    // Update the entry with queue position, scripts, and topic name
    const updatedEntry = await db.entries.update({
      where: {
        id: entryId,
        userId: session.user.id, // Ensure user owns this entry
      },
      data: {
        isGenerating: true,
        queuePosition: newQueuePosition,
        scripts: scripts,
        topicName: topicName,
        updatedAt: new Date(),
      },
    });

    console.log("✅ Entry queued with position:", newQueuePosition);

    // If this is the first in queue, immediately start backend processing
    if (newQueuePosition === 1) {
      console.log("🎬 First in queue - starting backend processing immediately");
      await startBackendProcessing(entryId, scripts, topicName);
    }

    return NextResponse.json({
      success: true,
      entryId: updatedEntry.id,
      queuePosition: newQueuePosition,
    });
  } catch (error) {
    console.error("❌ Error starting generation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function startBackendProcessing(entryId: string, scripts: any[], topicName: string) {
  try {
    console.log("📤 Sending batch render request to backend for entry:", entryId);

    // Prepare batch render payload
    const renderPayload = {
      topicName: topicName,
      entryId: entryId,
      scripts: scripts.map((script) => ({
        manim_code: script.manim_code,
        description: script.description,
      })),
      priority: 0,
    };

    const response = await fetch(`${BACKEND_URL}/batch_render`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(renderPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Backend error: ${response.status} - ${errorText}`);
      throw new Error(`Backend error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log("✅ Backend processing started successfully:", result);
  } catch (error) {
    console.error("❌ Failed to start backend processing:", error);
    // Note: We don't throw here to avoid breaking the queue assignment
    // The job will remain in queue and can be retried
  }
}
