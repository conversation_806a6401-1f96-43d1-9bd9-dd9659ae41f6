{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/actions/auth.actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { signIn, signOut } from \"@/lib/auth\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\nexport const login = async (provider: string) => {\r\n  await signIn(provider, { redirectTo: \"/dashboard\" });\r\n  revalidatePath(\"/\");\r\n};\r\n\r\nexport const logout = async () => {\r\n  await signOut({ redirectTo: \"/\" });\r\n  revalidatePath(\"/\");\r\n};\r\n\r\nexport const resendLogin = async (email: string) => {\r\n  await signIn(\"resend\", {\r\n    email,\r\n    redirectTo: \"/dashboard\",\r\n  });\r\n  revalidatePath(\"/dashboard\");\r\n};"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;;;;AAEO,MAAM,QAAQ,OAAO;IAC1B,MAAM,CAAA,GAAA,kHAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QAAE,YAAY;IAAa;IAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;AACjB;AAEO,MAAM,SAAS;IACpB,MAAM,CAAA,GAAA,kHAAA,CAAA,UAAO,AAAD,EAAE;QAAE,YAAY;IAAI;IAChC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;AACjB;AAEO,MAAM,cAAc,OAAO;IAChC,MAAM,CAAA,GAAA,kHAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QACrB;QACA,YAAY;IACd;IACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;AACjB;;;IAhBa;IAKA;IAKA;;AAVA,+OAAA;AAKA,+OAAA;AAKA,+OAAA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/lib/quizSchema.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\n// Reusable difficulty enum\r\nconst difficultyEnum = z.enum([\"easy\", \"medium\", \"hard\"]);\r\n\r\n// Question types\r\nconst multipleChoiceSchema = z.object({\r\n  type: z.literal(\"multiple-choice\"),\r\n  question: z.string(),\r\n  options: z.array(z.string()).length(4), // exactly 4 options\r\n  correctAnswer: z.number().min(0).max(3),\r\n  explanation: z.string(),\r\n  difficulty: difficultyEnum,\r\n});\r\n\r\nconst trueFalseSchema = z.object({\r\n  type: z.literal(\"true-false\"),\r\n  question: z.string(),\r\n  correctAnswer: z.boolean(),\r\n  explanation: z.string(),\r\n  difficulty: difficultyEnum,\r\n});\r\n\r\nconst fillBlankSchema = z.object({\r\n  type: z.literal(\"fill-blank\"),\r\n  question: z.string(),\r\n  correctAnswer: z.string(),\r\n  explanation: z.string(),\r\n  difficulty: difficultyEnum,\r\n});\r\n\r\n// Full quiz schema\r\nexport const quizSchema = z.object({\r\n  title: z.string(),\r\n  description: z.string(),\r\n  questions: z.array(\r\n    z.discriminatedUnion(\"type\", [\r\n      multipleChoiceSchema,\r\n      trueFalseSchema,\r\n      fillBlankSchema,\r\n    ])\r\n  ),\r\n});\r\n\r\n// TypeScript type for strong typing\r\nexport type Quiz = z.infer<typeof quizSchema>;\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,2BAA2B;AAC3B,MAAM,iBAAiB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAQ;IAAU;CAAO;AAExD,iBAAiB;AACjB,MAAM,uBAAuB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,SAAS,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,MAAM,CAAC;IACpC,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACrC,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,YAAY;AACd;AAEA,MAAM,kBAAkB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,eAAe,iLAAA,CAAA,IAAC,CAAC,OAAO;IACxB,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,YAAY;AACd;AAEA,MAAM,kBAAkB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,iLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,YAAY;AACd;AAGO,MAAM,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM;IACf,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,WAAW,iLAAA,CAAA,IAAC,CAAC,KAAK,CAChB,iLAAA,CAAA,IAAC,CAAC,kBAAkB,CAAC,QAAQ;QAC3B;QACA;QACA;KACD;AAEL", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/actions/quiz.actions.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { db } from \"@/lib/db\";\r\nimport { quizSchema, Quiz } from \"@/lib/quizSchema\";\r\n\r\nexport async function createQuiz(data: {\r\n  userId: string;\r\n  prompt: string;\r\n  quiz: Quiz;\r\n  topicName: string\r\n}) {\r\n  const parseResult = quizSchema.safeParse(data.quiz);\r\n\r\n  if (!parseResult.success) {\r\n    console.error(\"Invalid quiz format\", parseResult.error.format());\r\n    throw new Error(\"Invalid quiz format\");\r\n  }\r\n\r\n  const validatedQuiz: Quiz = parseResult.data;\r\n\r\n  // Convert correctAnswer to string for each question\r\n  const quizForDb = {\r\n    ...validatedQuiz,\r\n    questions: validatedQuiz.questions.map((q) => ({\r\n      ...q,\r\n      correctAnswer: String(q.correctAnswer),\r\n    })),\r\n  };\r\n\r\n  return await db.entries.create({\r\n    data: {\r\n      userId: data.userId,\r\n      prompt: data.prompt,\r\n      quiz: quizForDb,\r\n      topicName: data.topicName,\r\n    },\r\n  });\r\n}\r\n\r\nexport async function deleteQuiz(id: string) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id },\r\n  });\r\n  \r\n  if (!entry) {\r\n    throw new Error(\"Quiz not found\");\r\n  }\r\n\r\n  const updated = await db.entries.update({\r\n    where: { id },\r\n    data: {\r\n      quiz: null,\r\n      hasGiven: false,\r\n      marks: null,\r\n    },\r\n  });\r\n\r\n  return {\r\n    success: true,\r\n    message: \"Quiz removed successfully\",\r\n    updated,\r\n  };\r\n}\r\n\r\nexport async function answeredQuiz(data: {\r\n  id: string;\r\n  marks: number;\r\n}) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id: data.id },\r\n  });\r\n\r\n  if (!entry) {\r\n    throw new Error(\"Quiz not found\");\r\n  }\r\n\r\n  const updated = await db.entries.update({\r\n    where: { id: data.id },\r\n    data: {\r\n      hasGiven: true,\r\n      marks: data.marks,\r\n    },\r\n  });\r\n\r\n  return {\r\n    success: true,\r\n    message: \"Quiz marked as answered\",\r\n    updated,\r\n  };\r\n}\r\n\r\nexport async function getQuizById(id: string) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id },\r\n  });\r\n\r\n  if (!entry) {\r\n    return null;\r\n  }\r\n\r\n  if (!entry.quiz) {\r\n    return null;\r\n  }\r\n\r\n  // If given\r\n  if (entry.hasGiven) {\r\n    return {\r\n      ...entry,\r\n      quiz: {\r\n        ...entry.quiz,\r\n        questions: entry.quiz.questions.map((q) => ({\r\n          ...q,\r\n          correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string\r\n        })),\r\n      },\r\n    };\r\n  }\r\n\r\n  return entry;\r\n}\r\n\r\nexport async function getUserEntries(userId: string) {\r\n  const entries = await db.entries.findMany({\r\n    where: { userId },\r\n    orderBy: { createdAt: \"desc\" },\r\n  });\r\n\r\n  return entries.map((entry) => ({\r\n    ...entry,\r\n    quiz: entry.quiz\r\n      ? {\r\n          ...entry.quiz,\r\n          questions: entry.quiz.questions.map((q) => ({\r\n            ...q,\r\n            correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string\r\n          })),\r\n        }\r\n      : null,\r\n  }));\r\n}"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;;;;AAEO,eAAe,WAAW,IAKhC;IACC,MAAM,cAAc,wHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,KAAK,IAAI;IAElD,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB,QAAQ,KAAK,CAAC,uBAAuB,YAAY,KAAK,CAAC,MAAM;QAC7D,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,gBAAsB,YAAY,IAAI;IAE5C,oDAAoD;IACpD,MAAM,YAAY;QAChB,GAAG,aAAa;QAChB,WAAW,cAAc,SAAS,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;gBAC7C,GAAG,CAAC;gBACJ,eAAe,OAAO,EAAE,aAAa;YACvC,CAAC;IACH;IAEA,OAAO,MAAM,gHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;QAC7B,MAAM;YACJ,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;YACnB,MAAM;YACN,WAAW,KAAK,SAAS;QAC3B;IACF;AACF;AAEO,eAAe,WAAW,EAAU;IACzC,MAAM,QAAQ,MAAM,gHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE;QAAG;IACd;IAEA,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,MAAM,gHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;QACtC,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,MAAM;YACN,UAAU;YACV,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAEO,eAAe,aAAa,IAGlC;IACC,MAAM,QAAQ,MAAM,gHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE,IAAI,KAAK,EAAE;QAAC;IACvB;IAEA,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,MAAM,gHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;QACtC,OAAO;YAAE,IAAI,KAAK,EAAE;QAAC;QACrB,MAAM;YACJ,UAAU;YACV,OAAO,KAAK,KAAK;QACnB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,QAAQ,MAAM,gHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE;QAAG;IACd;IAEA,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,IAAI,CAAC,MAAM,IAAI,EAAE;QACf,OAAO;IACT;IAEA,WAAW;IACX,IAAI,MAAM,QAAQ,EAAE;QAClB,OAAO;YACL,GAAG,KAAK;YACR,MAAM;gBACJ,GAAG,MAAM,IAAI;gBACb,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wBAC1C,GAAG,CAAC;wBACJ,eAAe,OAAO,EAAE,aAAa;oBACvC,CAAC;YACH;QACF;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,MAAc;IACjD,MAAM,UAAU,MAAM,gHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;QACxC,OAAO;YAAE;QAAO;QAChB,SAAS;YAAE,WAAW;QAAO;IAC/B;IAEA,OAAO,QAAQ,GAAG,CAAC,CAAC,QAAU,CAAC;YAC7B,GAAG,KAAK;YACR,MAAM,MAAM,IAAI,GACZ;gBACE,GAAG,MAAM,IAAI;gBACb,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wBAC1C,GAAG,CAAC;wBACJ,eAAe,OAAO,EAAE,aAAa;oBACvC,CAAC;YACH,IACA;QACN,CAAC;AACH;;;IAtIsB;IAkCA;IAyBA;IA2BA;IA8BA;;AApHA,+OAAA;AAkCA,+OAAA;AAyBA,+OAAA;AA2BA,+OAAA;AA8BA,+OAAA", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/.next-internal/server/app/%28app%29/library/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getAuthenticatedUser as '00aa565eb2d0e4f80facd334f98ad09567eff45fb7'} from 'ACTIONS_MODULE0'\nexport {getUserFromEntriesId as '4094e98bfb36c131131e02aa1472a5a3bb4ba9450a'} from 'ACTIONS_MODULE1'\nexport {getUser as '40dbc3ead418eae12aee42777527fdfbc82310cbbf'} from 'ACTIONS_MODULE1'\nexport {logout as '7fcbbbed3abb26571f88b42ddd8f89a5fdca9cd647'} from 'ACTIONS_MODULE2'\nexport {getUserEntries as '40333d092027a9dc35ba3db39c375ec4e6f2805d34'} from 'ACTIONS_MODULE3'\n"], "names": [], "mappings": ";AAAA;AACA;AAEA;AACA", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/%28app%29/library/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(app)/library/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(app)/library/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/%28app%29/library/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(app)/library/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(app)/library/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}