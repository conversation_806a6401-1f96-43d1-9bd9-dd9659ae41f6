self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00aa565eb2d0e4f80facd334f98ad09567eff45fb7\": {\n      \"workers\": {\n        \"app/(app)/generate/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/generate/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(app)/library/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(app)/generate/page\": \"rsc\",\n        \"app/(app)/library/page\": \"rsc\"\n      }\n    },\n    \"4094e98bfb36c131131e02aa1472a5a3bb4ba9450a\": {\n      \"workers\": {\n        \"app/(app)/generate/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/generate/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(app)/library/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(app)/generate/page\": \"rsc\",\n        \"app/(app)/library/page\": \"rsc\"\n      }\n    },\n    \"40dbc3ead418eae12aee42777527fdfbc82310cbbf\": {\n      \"workers\": {\n        \"app/(app)/generate/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/generate/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(app)/library/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(app)/generate/page\": \"rsc\",\n        \"app/(app)/library/page\": \"rsc\"\n      }\n    },\n    \"7fcbbbed3abb26571f88b42ddd8f89a5fdca9cd647\": {\n      \"workers\": {\n        \"app/(app)/generate/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/generate/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(app)/library/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(app)/generate/page\": \"action-browser\",\n        \"app/(app)/library/page\": \"action-browser\"\n      }\n    },\n    \"40333d092027a9dc35ba3db39c375ec4e6f2805d34\": {\n      \"workers\": {\n        \"app/(app)/library/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(app)/library/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/authenticatedUser.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/user.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/actions/auth.actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE3 => \\\"[project]/src/actions/quiz.actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(app)/library/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"dVF7qFKM2cQ8/z46hw4X7IjD66wh96fiTI69TXHPOYA=\"\n}"