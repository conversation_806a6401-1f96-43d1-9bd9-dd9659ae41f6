{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\r\n\r\ndeclare global {\r\n  var prisma: PrismaClient | undefined;\r\n}\r\n\r\nexport const db = globalThis.prisma || new PrismaClient();\r\n\r\nif (process.env.NODE_ENV !== \"production\") {\r\n  globalThis.prisma = db;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAMO,MAAM,KAAK,WAAW,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,wCAA2C;IACzC,WAAW,MAAM,GAAG;AACtB", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/lib/quizSchema.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\n// Reusable difficulty enum\r\nconst difficultyEnum = z.enum([\"easy\", \"medium\", \"hard\"]);\r\n\r\n// Question types\r\nconst multipleChoiceSchema = z.object({\r\n  type: z.literal(\"multiple-choice\"),\r\n  question: z.string(),\r\n  options: z.array(z.string()).length(4), // exactly 4 options\r\n  correctAnswer: z.number().min(0).max(3),\r\n  explanation: z.string(),\r\n  difficulty: difficultyEnum,\r\n});\r\n\r\nconst trueFalseSchema = z.object({\r\n  type: z.literal(\"true-false\"),\r\n  question: z.string(),\r\n  correctAnswer: z.boolean(),\r\n  explanation: z.string(),\r\n  difficulty: difficultyEnum,\r\n});\r\n\r\nconst fillBlankSchema = z.object({\r\n  type: z.literal(\"fill-blank\"),\r\n  question: z.string(),\r\n  correctAnswer: z.string(),\r\n  explanation: z.string(),\r\n  difficulty: difficultyEnum,\r\n});\r\n\r\n// Full quiz schema\r\nexport const quizSchema = z.object({\r\n  title: z.string(),\r\n  description: z.string(),\r\n  questions: z.array(\r\n    z.discriminatedUnion(\"type\", [\r\n      multipleChoiceSchema,\r\n      trueFalseSchema,\r\n      fillBlankSchema,\r\n    ])\r\n  ),\r\n});\r\n\r\n// TypeScript type for strong typing\r\nexport type Quiz = z.infer<typeof quizSchema>;\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,2BAA2B;AAC3B,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAQ;IAAU;CAAO;AAExD,iBAAiB;AACjB,MAAM,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,SAAS,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,MAAM,CAAC;IACpC,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACrC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,YAAY;AACd;AAEA,MAAM,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,eAAe,mLAAA,CAAA,IAAC,CAAC,OAAO;IACxB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,YAAY;AACd;AAEA,MAAM,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;IAClB,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM;IACvB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,YAAY;AACd;AAGO,MAAM,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM;IACf,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,WAAW,mLAAA,CAAA,IAAC,CAAC,KAAK,CAChB,mLAAA,CAAA,IAAC,CAAC,kBAAkB,CAAC,QAAQ;QAC3B;QACA;QACA;KACD;AAEL", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/actions/quiz.actions.ts"], "sourcesContent": ["\"use server\"\r\n\r\nimport { db } from \"@/lib/db\";\r\nimport { quizSchema, Quiz } from \"@/lib/quizSchema\";\r\n\r\nexport async function createQuiz(data: {\r\n  userId: string;\r\n  prompt: string;\r\n  quiz: Quiz;\r\n  topicName: string\r\n}) {\r\n  const parseResult = quizSchema.safeParse(data.quiz);\r\n\r\n  if (!parseResult.success) {\r\n    console.error(\"Invalid quiz format\", parseResult.error.format());\r\n    throw new Error(\"Invalid quiz format\");\r\n  }\r\n\r\n  const validatedQuiz: Quiz = parseResult.data;\r\n\r\n  // Convert correctAnswer to string for each question\r\n  const quizForDb = {\r\n    ...validatedQuiz,\r\n    questions: validatedQuiz.questions.map((q) => ({\r\n      ...q,\r\n      correctAnswer: String(q.correctAnswer),\r\n    })),\r\n  };\r\n\r\n  return await db.entries.create({\r\n    data: {\r\n      userId: data.userId,\r\n      prompt: data.prompt,\r\n      quiz: quizForDb,\r\n      topicName: data.topicName,\r\n    },\r\n  });\r\n}\r\n\r\nexport async function deleteQuiz(id: string) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id },\r\n  });\r\n  \r\n  if (!entry) {\r\n    throw new Error(\"Quiz not found\");\r\n  }\r\n\r\n  const updated = await db.entries.update({\r\n    where: { id },\r\n    data: {\r\n      quiz: null,\r\n      hasGiven: false,\r\n      marks: null,\r\n    },\r\n  });\r\n\r\n  return {\r\n    success: true,\r\n    message: \"Quiz removed successfully\",\r\n    updated,\r\n  };\r\n}\r\n\r\nexport async function answeredQuiz(data: {\r\n  id: string;\r\n  marks: number;\r\n}) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id: data.id },\r\n  });\r\n\r\n  if (!entry) {\r\n    throw new Error(\"Quiz not found\");\r\n  }\r\n\r\n  const updated = await db.entries.update({\r\n    where: { id: data.id },\r\n    data: {\r\n      hasGiven: true,\r\n      marks: data.marks,\r\n    },\r\n  });\r\n\r\n  return {\r\n    success: true,\r\n    message: \"Quiz marked as answered\",\r\n    updated,\r\n  };\r\n}\r\n\r\nexport async function getQuizById(id: string) {\r\n  const entry = await db.entries.findUnique({\r\n    where: { id },\r\n  });\r\n\r\n  if (!entry) {\r\n    return null;\r\n  }\r\n\r\n  if (!entry.quiz) {\r\n    return null;\r\n  }\r\n\r\n  // If given\r\n  if (entry.hasGiven) {\r\n    return {\r\n      ...entry,\r\n      quiz: {\r\n        ...entry.quiz,\r\n        questions: entry.quiz.questions.map((q) => ({\r\n          ...q,\r\n          correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string\r\n        })),\r\n      },\r\n    };\r\n  }\r\n\r\n  return entry;\r\n}\r\n\r\nexport async function getUserEntries(userId: string) {\r\n  const entries = await db.entries.findMany({\r\n    where: { userId },\r\n    orderBy: { createdAt: \"desc\" },\r\n  });\r\n\r\n  return entries.map((entry) => ({\r\n    ...entry,\r\n    quiz: entry.quiz\r\n      ? {\r\n          ...entry.quiz,\r\n          questions: entry.quiz.questions.map((q) => ({\r\n            ...q,\r\n            correctAnswer: String(q.correctAnswer), // Ensure correctAnswer is a string\r\n          })),\r\n        }\r\n      : null,\r\n  }));\r\n}"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;;;;AAEO,eAAe,WAAW,IAKhC;IACC,MAAM,cAAc,0HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,KAAK,IAAI;IAElD,IAAI,CAAC,YAAY,OAAO,EAAE;QACxB,QAAQ,KAAK,CAAC,uBAAuB,YAAY,KAAK,CAAC,MAAM;QAC7D,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,gBAAsB,YAAY,IAAI;IAE5C,oDAAoD;IACpD,MAAM,YAAY;QAChB,GAAG,aAAa;QAChB,WAAW,cAAc,SAAS,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;gBAC7C,GAAG,CAAC;gBACJ,eAAe,OAAO,EAAE,aAAa;YACvC,CAAC;IACH;IAEA,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;QAC7B,MAAM;YACJ,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;YACnB,MAAM;YACN,WAAW,KAAK,SAAS;QAC3B;IACF;AACF;AAEO,eAAe,WAAW,EAAU;IACzC,MAAM,QAAQ,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE;QAAG;IACd;IAEA,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;QACtC,OAAO;YAAE;QAAG;QACZ,MAAM;YACJ,MAAM;YACN,UAAU;YACV,OAAO;QACT;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAEO,eAAe,aAAa,IAGlC;IACC,MAAM,QAAQ,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE,IAAI,KAAK,EAAE;QAAC;IACvB;IAEA,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;QACtC,OAAO;YAAE,IAAI,KAAK,EAAE;QAAC;QACrB,MAAM;YACJ,UAAU;YACV,OAAO,KAAK,KAAK;QACnB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,QAAQ,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE;QAAG;IACd;IAEA,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,IAAI,CAAC,MAAM,IAAI,EAAE;QACf,OAAO;IACT;IAEA,WAAW;IACX,IAAI,MAAM,QAAQ,EAAE;QAClB,OAAO;YACL,GAAG,KAAK;YACR,MAAM;gBACJ,GAAG,MAAM,IAAI;gBACb,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wBAC1C,GAAG,CAAC;wBACJ,eAAe,OAAO,EAAE,aAAa;oBACvC,CAAC;YACH;QACF;IACF;IAEA,OAAO;AACT;AAEO,eAAe,eAAe,MAAc;IACjD,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;QACxC,OAAO;YAAE;QAAO;QAChB,SAAS;YAAE,WAAW;QAAO;IAC/B;IAEA,OAAO,QAAQ,GAAG,CAAC,CAAC,QAAU,CAAC;YAC7B,GAAG,KAAK;YACR,MAAM,MAAM,IAAI,GACZ;gBACE,GAAG,MAAM,IAAI;gBACb,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wBAC1C,GAAG,CAAC;wBACJ,eAAe,OAAO,EAAE,aAAa;oBACvC,CAAC;YACH,IACA;QACN,CAAC;AACH;;;IAtIsB;IAkCA;IAyBA;IA2BA;IA8BA;;AApHA,iPAAA;AAkCA,iPAAA;AAyBA,iPAAA;AA2BA,iPAAA;AA8BA,iPAAA", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/clarifai/frontend/src/app/api/ai-quiz/route.ts"], "sourcesContent": ["import { generateObject } from \"ai\";\r\nimport { z } from \"zod\";\r\nimport { groq } from \"@ai-sdk/groq\";\r\nimport { NextRequest, NextResponse } from \"next/server\";\r\nimport { createQuiz } from \"@/actions/quiz.actions\";\r\n\r\n// Define the quiz schema with different question types\r\nconst quizSchema = z.object({\r\n  title: z.string(),\r\n  description: z.string(),\r\n  questions: z.array(\r\n    z.discriminatedUnion(\"type\", [\r\n      // Multiple choice questions\r\n      z.object({\r\n        type: z.literal(\"multiple-choice\"),\r\n        question: z.string(),\r\n        options: z.array(z.string()),\r\n        correctAnswer: z.number(),\r\n        explanation: z.string(),\r\n        difficulty: z.enum([\"easy\", \"medium\", \"hard\"]),\r\n      }),\r\n      // True/False questions\r\n      z.object({\r\n        type: z.literal(\"true-false\"),\r\n        question: z.string(),\r\n        correctAnswer: z.boolean(),\r\n        explanation: z.string(),\r\n        difficulty: z.enum([\"easy\", \"medium\", \"hard\"]),\r\n      }),\r\n      // Fill in the blank questions\r\n      z.object({\r\n        type: z.literal(\"fill-blank\"),\r\n        question: z.string(),\r\n        correctAnswer: z.string(),\r\n        explanation: z.string(),\r\n        difficulty: z.enum([\"easy\", \"medium\", \"hard\"]),\r\n      }),\r\n    ])\r\n  ),\r\n});\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const { title, content, userId, topicName } = await request.json();\r\n\r\n    if (!content) {\r\n      return NextResponse.json(\r\n        { error: \"Missing required parameters\" },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const quiz = await generateObject({\r\n      model: groq(\"meta-llama/llama-4-scout-17b-16e-instruct\"),\r\n      schemaName: \"Quiz\",\r\n      schema: quizSchema,\r\n      system: `You are given a narrative script that explains concepts related on a given set of topics related to a major topic. Your task is to generate a comprehensive quiz based on the content of the script. The quiz should include various types of questions to test the learner's understanding of the material. Based strictly on the content of the script, generate a comprehensive quiz with questions that:\r\n\r\n      Include a mix of:\r\n\r\n      1) Multiple-choice questions (type: 'multiple-choice')\r\n        - 4 answer options per question\r\n        - Only one correct answer per question\r\n        - correctAnswer: index of the correct option (0-3)\r\n        - Include a detailed explanation\r\n        - Assign a difficulty: easy, medium, or hard\r\n\r\n      2) True/False questions (type: 'true-false')\r\n        - correctAnswer: true or false\r\n        - Include a detailed explanation\r\n        - Assign a difficulty: easy, medium, or hard\r\n\r\n      3) Fill-in-the-blank questions (type: 'fill-blank')\r\n        - A sentence with a blank (_____) for the learner to complete\r\n        - correctAnswer: the word or phrase that fills the blank\r\n        - Include a detailed explanation\r\n        - Assign a difficulty: easy, medium, or hard\r\n\r\n      📌 Guidelines\r\n        - The number of questions should depend strictly on the script's length and content depth (can be 5 or fewer, or 15+ if content is long).\r\n        - Ensure a balanced mix of easy, medium, and hard questions.\r\n\r\n      Questions should:\r\n\r\n        - Be based only on the provided script\r\n        - Test both factual recall and conceptual understanding\r\n        - Be clear, concise, and unambiguous\r\n        - Be suitable for students learning networking concepts`,\r\n      prompt: `Generate a quiz on the topic \"${title}\" based on the content provided in the script. The script is as follows: ${content}`,\r\n    });\r\n\r\n    const quizObj = \"object\" in quiz ? quiz.object : quiz;\r\n    const validatedQuiz = quizSchema.safeParse(quizObj);\r\n\r\n    const quizData = await createQuiz({\r\n      userId,\r\n      prompt: title,\r\n      quiz: validatedQuiz.success ? validatedQuiz.data : quizObj,\r\n      topicName,\r\n    });\r\n\r\n    if (!quizData) {\r\n      return NextResponse.json(\r\n        { error: \"Failed to create quiz\" },\r\n        { status: 500 }\r\n      );\r\n    }\r\n\r\n    console.log(\"Quiz generated successfully:\", quizData);\r\n    return NextResponse.json(quizData, { status: 200 });\r\n  } catch (error) {\r\n    console.error(\"Error generating quiz:\", error);\r\n    return NextResponse.json(\r\n      { error: \"Failed to generate quiz\" },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,uDAAuD;AACvD,MAAM,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM;IACf,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;IACrB,WAAW,mLAAA,CAAA,IAAC,CAAC,KAAK,CAChB,mLAAA,CAAA,IAAC,CAAC,kBAAkB,CAAC,QAAQ;QAC3B,4BAA4B;QAC5B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,MAAM,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;YAChB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;YAClB,SAAS,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM;YACzB,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM;YACvB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;YACrB,YAAY,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAQ;gBAAU;aAAO;QAC/C;QACA,uBAAuB;QACvB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,MAAM,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;YAChB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;YAClB,eAAe,mLAAA,CAAA,IAAC,CAAC,OAAO;YACxB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;YACrB,YAAY,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAQ;gBAAU;aAAO;QAC/C;QACA,8BAA8B;QAC9B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACP,MAAM,mLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;YAChB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM;YAClB,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM;YACvB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM;YACrB,YAAY,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAQ;gBAAU;aAAO;QAC/C;KACD;AAEL;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEhE,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,EAAE;YAChC,OAAO,CAAA,GAAA,uJAAA,CAAA,OAAI,AAAD,EAAE;YACZ,YAAY;YACZ,QAAQ;YACR,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+DA+BgD,CAAC;YAC1D,QAAQ,CAAC,8BAA8B,EAAE,MAAM,yEAAyE,EAAE,SAAS;QACrI;QAEA,MAAM,UAAU,YAAY,OAAO,KAAK,MAAM,GAAG;QACjD,MAAM,gBAAgB,WAAW,SAAS,CAAC;QAE3C,MAAM,WAAW,MAAM,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;YAChC;YACA,QAAQ;YACR,MAAM,cAAc,OAAO,GAAG,cAAc,IAAI,GAAG;YACnD;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwB,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}