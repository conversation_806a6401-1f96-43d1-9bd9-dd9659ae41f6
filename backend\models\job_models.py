from datetime import datetime, timezone
from enum import Enum
from typing import Optional, List
from pydantic import BaseModel, Field
import uuid


class JobStatus(str, Enum):
    """Job status enumeration for direct processing."""

    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class JobType(str, Enum):
    """Job type enumeration."""

    RENDER = "render"
    BATCH_RENDER = "batch_render"
    TOPIC_RENDER = "topic_render"


class RenderJobData(BaseModel):
    """Data model for a single render job."""

    script: str
    scene_name: Optional[str] = None
    entry_id: Optional[str] = Field(
        default=None, description="Entry ID to update after rendering"
    )


class ManimScriptData(BaseModel):
    """Data model for a single manim script with description."""

    manim_code: str
    description: str


class BatchRenderJobData(BaseModel):
    """Data model for a batch render job."""

    scripts: List[RenderJobData]
    entry_id: Optional[str] = Field(
        default=None, description="Entry ID to update after batch rendering"
    )


class TopicRenderJobData(BaseModel):
    """Data model for a topic-based batch render job with manim scripts."""

    topic_name: str
    entry_id: str
    scripts: List[ManimScriptData]


class JobRequest(BaseModel):
    """Base job request model for direct processing."""

    job_id: str = Field(default_factory=lambda: uuid.uuid4().hex)
    job_type: JobType
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Job-specific data
    render_data: Optional[RenderJobData] = None
    batch_render_data: Optional[BatchRenderJobData] = None
    topic_render_data: Optional[TopicRenderJobData] = None


class JobResult(BaseModel):
    """Job result model."""

    job_id: str
    success: bool
    output_urls: List[str] = Field(default_factory=list)
    error_message: Optional[str] = None
    processing_time_seconds: Optional[float] = None
    completed_at: Optional[datetime] = None


# Removed JobStatusTracker and QueueStats as they are not needed for direct processing


# Request/Response models for API endpoints
class CreateRenderJobRequest(BaseModel):
    """Request model for creating a render job."""

    script: str
    scene_name: Optional[str] = None
    entry_id: Optional[str] = Field(
        default=None, description="Entry ID to update after rendering"
    )


class CreateTopicRenderJobRequest(BaseModel):
    """Request model for creating a topic-based render job with the new format."""

    topic_name: str = Field(alias="topicName")
    entry_id: str = Field(alias="entryId")
    scripts: List[ManimScriptData]

    class Config:
        populate_by_name = True


class DirectJobResponse(BaseModel):
    """Response model for direct job processing."""

    job_id: str
    status: JobStatus
    message: str
    result: Optional[JobResult] = None
