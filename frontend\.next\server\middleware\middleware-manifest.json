{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_cb73fd16._.js", "server/edge/chunks/node_modules_@auth_core_b5890d95._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_74ea7e62._.js", "server/edge/chunks/[root-of-the-server]__b683c460._.js", "server/edge/chunks/edge-wrapper_551d8963.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dVF7qFKM2cQ8/z46hw4X7IjD66wh96fiTI69TXHPOYA=", "__NEXT_PREVIEW_MODE_ID": "9a9104eaa4a7b3593c36361580ae21ec", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e50a8988150f61d644cbe197491017c5922de22f5ce03465f845250bb075cbe3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "75cb478a072d5d9c29e7fb77a291fedf9db89a889600c49c6b93e08e0c59b5d6"}}}, "instrumentation": null, "functions": {}}